<!doctype html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="icon" href="./favicon.png" />
    <link rel="shortcut icon" href="./favicon.png" />
    <link rel="apple-touch-icon" href="./favicon.png" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black" />
    <meta name="color-scheme" content="light dark" />
    <meta name="author" content="foxess.ru" />
    <title>FX Table</title>
    <meta name="description" content="FX Table" />
    <style>
        body {
            margin: 0;
            padding: 0;
            overflow: hidden;
            opacity: 0;
            height: 100vh;
            width: 100vw;
        }
        
        fx-table {
            display: block;
            height: 100%;
            width: 100%;
        }
    </style>
</head>

<body>
    <fx-table id="table_demo"></fx-table>
</body>

<script type="module">
    import './table.js';
    setTimeout(() => {
        //document.body.style.opacity = 1;
        table_demo.topLabel = 'top-label';
        table_demo.bottomLabel = 'bottom-label';
        table_demo.columns = [
            { field: 'id', header: 'ID', width: 64 },
            { field: 'selector1', header: 'S', width: 40, typeColumn: 'selector1', args: { size: 28, color1: 'violet' } },
            { field: 'selector', header: 'Selector', width:130, typeColumn: 'selector', args: { size: 28, type: '●' }, style: 'margin-top: -6px;' },
            { field: 'date', header: 'Date', width: 150, typeColumn: 'input', typeInput: 'date' },
            {
                field: 'name',
                header: 'Название',
                width: 150,
                typeColumn: 'input'
            },
            {
                field: 'kol',
                header: 'Количество',
                width: 100,
                typeColumn: 'input',
                textAlign: 'right',
                footerStyle: 'justify-content: end;'
            },
            {
                field: 'price',
                header: 'Цена',
                width: 100,
                typeColumn: 'input',
                textAlign: 'right',
                footerStyle: 'justify-content: end;'
            },
            {
                field: 'total',
                header: 'Итого',
                width: 120,
                typeColumn: 'span',
                textAlign: 'right',
                footerStyle: 'justify-content: end;',
                calc: (e) => (e.kol || 0) * (e.price || 0)
            },
            { field: 'ta', header: 'TA', width: 150, typeColumn: 'textarea', style: 'color: gray;' },
            { field: 'name', header: 'Name', width: 150, typeColumn: 'input' },
            { field: 'email', header: 'Email', width: 200 },
            { field: 'phone', header: 'Phone', width: 150, typeColumn: 'input', typeInput: 'tel' },
            { field: 'price', header: 'Price', width: 100 },
            { field: 'actions', header: 'Actions', width: 80, typeColumn: 'html' },
            { field: 'info', header: 'Inf', width: 32, typeColumn: 'info' },
            { field: 'note', header: 'Note', minWidth: 200, width: 'auto' },
        ]
        table_demo.data = Array.from({ length: 1000 }, (_, i) => ({
            id: i + 1,
            kol: Math.floor(1 + Math.random() * 10),
            name: `User ${i + 1}`,
            email: `user${i + 1}@example.com`,
            phone: `******-${Math.floor(1000 + Math.random() * 9000)}`,
            price: Math.floor(100 + Math.random() * 900),
            actions: '<fx-button width="auto">Edit</button>',
            selector: '0, 0, 0, 0, 0',
            selector1: '',
            date: '',
            ta: 'textarea ... textarea ... textarea ... textarea ...',
            note: 'note ... note ... note ... note ...'
        }))
        table_demo.footerCalculations = {
            id: { type: 'count' },
            price: { type: 'sum', decimals: 2 },
            name: { type: 'custom', customFn: (data) => `Total: ${data.length} users` },
            kol: { type: 'sum', decimals: 2 },
            total: { type: 'sum', decimals: 2 },
        }
    }, 100)
</script>

</html>
