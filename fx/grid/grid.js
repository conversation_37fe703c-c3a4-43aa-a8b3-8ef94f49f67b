import { FxElement, html } from '/fx.js';
import { $styles } from './grid.x.js';
import '../button/button.js';

export class FxGrid extends FxElement {
    static properties = {
        isReady: { type: Boolean },
        id: { type: String, default: '' },
        item: { type: Object },
        type: { type: String, default: 'grid' },
        io: { type: Object },
        fontSize: { type: String, default: 'medium' },
        selected: { type: Object, default: null },
        columnWidth: { type: Number, default: 240, save: true },
        rowHeight: { type: Number, default: 24 },
        hideSelected: { type: Boolean },
        expandeds: { type: Object, default: {} }
    }

    firstUpdated() {
        super.firstUpdated();
        this.async(() => {
            if (this.columnWidth < 0 || this.columnWidth > this.offsetWidth - 100) this.columnWidth = this.offsetWidth - 100;
            document.body.style.opacity = this.style.opacity = 1;
            this.getData();
            this.isReady = true;
        }, 100)
    }

    async getData() {
        if (this.item) return;
        let io = this.isShowFocused ? this.focused.el || this.focused || this.io : this.io;
        io = await makeData(io);
        this.item = typeof io === 'object' && io?.items?.length === 1 ? io.items[0] : io;
        this.item.expanded = true;
        this.$update();
    }

    toggleExpand(item) {
        item.expanded = !item.expanded;
        this.$update();
    }
    _select(e, item) {
        if (!this.hideSelected)
            this.selected = item;
        this.fire('selected', item);
        this.async(() => this.$update(), 20);
        this.$update();
    }
    _setValue(e, item) {
        if (e.target.localName === 'fx-checkbox') item.value = e.target.toggled;
        else if (item.is !== 'button') item.value = e.target.value;
        if (item?.set) {
            item.set?.(item.value);
        }
        item?.run?.(e, item);
        // console.log(item);
    }

    static styles = [$styles]

    renderValue(item) {
        let value = item.value || '',
            type = item.type || 'text',
            is = item.is || 'input',
            selected = this.selected === item;
        if (typeof item.value === 'function') {
            value = value();
        } else if (item.get) {
            value = item.get();
        }
        return html`
            <div class="horizontal relative w100 h100" @click=${(e) => this._select(e, item)} style="cz-index: 100; font-size: medium; width: ${this.columnWidth}px;">
                ${is === 'input' ? html`<input type=${type} class="ml4 mr4 inp w100 h100 fm ${selected ? 'selected' : ''}" .value=${value || ''} @input=${(e) => this._setValue(e, item)}>
                ` : is === 'textarea' ? html`<textarea class="ml4 mr4 inp w100 h100 fm ${selected ? 'selected' : ''}" .value=${value || ''} @input=${(e) => this._setValue(e, item)}></textarea>
                ` : is === 'button' ? html`<fx-button class="m4 w100 h100 pointer" width="100%" @click=${(e) => this._setValue(e, item)}>${value || ''}</fx-button>
                ` : is === 'checkbox' ? html`<fx-checkbox class="horizontal w100 p2 center" @click=${(e) => this._setValue(e, item)} .toggled=${value}></fx-checkbox>
                ` : is === 'span' ? html`<span class="horizontal w100 p2">${value}</span>
                ` : is === 'list' ? html`
                    <input class="inp w100 fm pl4 pr4 ${item.class || ''}" type="search"
                        .value=${value || ''}
                        style="color: ${this.selected ? 'white' : 'inherit'}; ${item.style || ''}"
                        list="list"
                        @input=${(e) => this._setValue(e, item)}
                    >
                    <datalist id="list">${(item.dataList || []).sort().map(o => html`
                        <option value=${o}\>`)}
                    </datalist>
                ` : is === 'select' ? html`
                    <select class="inp fm w100 ${item.class || ''}"
                        style="color: ${this.selected ? 'white' : 'inherit'}; ${item.style || ''}"
                        @change=${(e) => this._setValue(e, item)}
                    >
                        ${(item.options || []).map(o => html` <option value=${o} .selected=${value === o} style="background: #ddd!important; color: #333!important;">${o}</option>`)}
                    </select>
                ` : is === 'sbutton' ? html`
                    <div class="horizontal flex w100 relative">
                        <select class="inp fm w100 ${item.class || ''}"
                            style="color: ${this.selected ? 'white' : 'inherit'}; ${item.style || ''}"
                            @change=${(e) => this._setValue(e, { id: 'sbutton', run: item.run })}
                        >
                            ${(item.options || []).map(o => html` <option value=${o} .selected=${value === o} style="background: #ddd!important; color: #333!important;">${o}</option>`)}
                        </select>
                        <fx-icon class="but mr4 ml4" icon="carbon:add" scale=1 an="btn" br="square" @click=${(e) => this._setValue(e, item)}></fx-icon>
                    </div>
                ` : html`<span class="ml4 mr4 horizontal align w100 h100 pointer" style="height: 24px">${value}</span>`}
            </div>
        `
    }

    renderTreeItem(item, level = 0) {
        const hasChildren = item.items && item.items.length > 0,
            selected = this.selected === item;
        if (item.hidden) return html``;
        return html`
            <div class="tree-item horizontal align flex brb ${selected ? 'selected' : ''}" @click=${(e) => this._select(e, item)} 
                style="padding-left: ${level * 16}px; min-height: ${this.rowHeight || 24}px;">
                <div class="tree-label-container p4 pr8" style="display: flex; align-items: center; flex: 1; overflow: hidden;">
                    ${hasChildren ? html`
                        <fx-icon url="cb-chevron-right"
                            @click=${(e) => { e.stopPropagation(); this.toggleExpand(item); }}
                            style="transform: rotate(${item.expanded ? '90deg' : '0deg'}); transition: transform 0.2s; margin-left: -2px; min-width: 0px!important;"
                        ></fx-icon>
                    ` : html``}
                    ${item.icon || item.svg ? html`
                        <fx-icon class="-ml2" url="${item.icon}" svg=${item.svg} style="min-width: 0px!important;" fill=${item.fill} br=${item.br} size=${item.size || 24} scale=${item.scale}></fx-icon>
                    ` : ''}
                    <div class="vertical flex w100 relative ellipsis justify" style="min-width: 0px;">
                        <span class="horizontal inp flex ml4 ellipsis align ${item.labClass || ''}" title=${item.label} style="font-size:${item.labSize || this.fontSize}; ${item.labStyle || ''}">${item.label}</span>
                        ${item.subLabel ? html`
                            <span class="horizontal inp flex ml4 ellipsis align fxxs ${item.subClass || ''}" title=${item.suLabel}  style="${item.subStyle || ''};">${item.subLabel}</span>
                        ` : html``}

                    </div>
                </div>          
                ${this.renderValue(item)}
            </div>
            ${hasChildren && item.expanded ? html`
                <div class="tree-children grid2 ml16">
                    ${item.items.map(child => this.renderTreeItem(child, level + 1))}
                </div>
            ` : ''}
        `
    }

    render() {
        if (!this.item) return html``;
        return html`
            <div class="tree-container h100 w100 relative oveflow-h overflow-y panel">
                ${this.item.map ? this.item.map(i => this.renderTreeItem(i)) : this.renderTreeItem(this.item)}
            </div>
            <div class="splitter" @pointerdown=${(e) => this.#resizeHandler.startResize(e)} style="position: absolute; right: ${this.columnWidth}px;"></div>
        `
    }

    #resizeHandler = {
        startResize: (e) => {
            this.resizing = true;
            this.startX = e.clientX;
            this.startWidth = this.columnWidth;
            document.addEventListener('pointermove', this.#resizeHandler.onResize);
            document.addEventListener('pointerup', this.#resizeHandler.stopResize);
            document.addEventListener('pointerleave', this.#resizeHandler.stopResize);
            e.preventDefault();
            e.stopPropagation();
        },
        onResize: (e) => {
            if (!this.resizing) return;
            const delta = e.clientX - this.startX;
            this.columnWidth = Math.max(100, Math.min(this.offsetWidth - 100, this.startWidth - delta));
            this.requestUpdate();
        },
        stopResize: () => {
            if (!this.resizing) return;
            this.resizing = false;
            document.removeEventListener('pointermove', this.#resizeHandler.onResize);
            document.removeEventListener('pointerup', this.#resizeHandler.stopResize);
            document.removeEventListener('pointerleave', this.#resizeHandler.stopResize);
        }
    }
}
customElements.define('fx-grid', FxGrid);

function makeData(obj) {
    if (obj === null || obj === undefined) {
        return { value: String(obj) };
    }
    if (Array.isArray(obj)) {
        return {
            label: 'Array',
            value: 'Array [' + obj.length + ']',
            items: obj.map((item, idx) => ({
                label: '' + idx,
                ...(typeof item === 'object' && item !== null ? makeData(item) : { value: item })
            }))
        };
    } else if (typeof obj === 'object') {
        const items = Object.keys(obj).map(key => {
            const value = obj[key];
            if (Array.isArray(value)) {
                return {
                    label: key,
                    value: 'Array [' + value.length + ']',
                    items: value.map((item, idx) => ({
                        label: '' + idx,
                        ...(typeof item === 'object' && item !== null ? makeData(item) : { value: item })
                    }))
                };
            } else if (typeof value === 'object' && value !== null) {
                return {
                    label: key,
                    value: 'Object',
                    ...makeData(value)
                }
            } else {
                return {
                    label: key,
                    value: value
                }
            }
        })
        return { items };
    }
    return { value: obj };
}
